[package]
name = "lledit"
version = "0.1.0"
edition = "2021"

[dependencies]

arboard = { version = "3.4.0", features = ["wayland-data-control"] }
axum = { version = "0.7", features = ["macros"] }
utoipa = { version = "4.2.3", features = ["axum_extras", "chrono"] }
utoipa-swagger-ui = { version = "6.0.0", features = ["axum"] }
tokio = { version = "1", features = ["full"] }
clap = { version = "4.4", features = ["derive"] }
regex = "1.11.1" # Used by string_sanitizer
thiserror = "1.0"
futures = "0.3" # For StreamExt
serde_json = "1.0.140"
serde = { version = "1.0", features = ["derive"] }
sha2 = "0.10" # Added for content hashing
log = "0.4"
env_logger = "0.10"
chrono = "0.4"
colored = "2"
indicatif = "0.17"
reqwest = { version = "0.12", features = ["json"] } # Added reqwest
url = "2.5.0" # Added for URL parsing
rustdoc-md = { git = "https://github.com/RockasMockas/rustdoc-md", branch = "main" }
async-trait = "0.1"
serde_yaml = "0.9"
directories = "5.0"
dunce = "1.0"
syn = { version = "2.0", features = ["full"] }
ratatui = { version = "0.29.0", features = [
  "crossterm",
  "serde",
] } # Updated ratatui
crossterm = { version = "0.27.0", features = [
  "serde",
] } # crossterm 0.27.0 is compatible with ratatui 0.29.0
once_cell = "1.19"
fuzzy-matcher = "0.3.7"
genai = "0.3.5"
rustc_lexer = "0.1" # Added for accurate delimiter checking
wl-clipboard-rs = "0.9.2"
rodio = "0.19" # Audio playback for sound system


[dev-dependencies]
tempfile = "3.10" # Added for temporary file/directory creation in tests
