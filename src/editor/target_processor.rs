use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::replace_blocks::ReplaceBlockParser;
use crate::block_parsing::traits::ParsableBlock;
use crate::config::AppConfig;
use crate::editor::types::{EditTarget, ProposedEdit};
// use crate::files::file_handler::LabeledFile; // Corrected path - LabeledFile is unused here
use crate::llm::client::chat_inference;
use crate::llm::{ChatMessage, ChatRole, LLMClient, MessageType};
use crate::prompt_builder::construct_specific_edit_prompt;
// crate::result::Result removed as unused
// LLMSpinner removed
use log::{debug, error, trace, warn};
// use std::collections::HashMap; // Unused
use std::env;
use std::path::PathBuf;
use std::sync::Arc; // Added import

// Moved from old src/editor.rs
// Renamed to be pub(crate)
#[allow(clippy::too_many_arguments)] // Keep original allow if present
pub(crate) async fn _handle_process_target(
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,        // Changed to Arc
    historical_context: &[ChatMessage],      // Added historical_context
    conversation_log: &mut Vec<ChatMessage>, // This is messages_for_current_task
    // _result_handler: &mut Result, // Parameter removed
    ordered_files: &crate::files::ordered_files::OrderedFiles, // Changed to OrderedFiles
    target_info: &EditTarget,
) -> std::result::Result<(Option<ProposedEdit>, bool), String> {
    // Explicitly use std::result::Result
    let current_dir = env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
    let display_path = target_info
        .file_path
        .strip_prefix(&current_dir)
        .unwrap_or(&target_info.file_path);

    // Level 3: Detailed processing step
    log::trace!(
        "--- Processing Target: {} lines {}-{} ---",
        display_path.display(),
        target_info.start_line,
        target_info.end_line
    );

    if let Some(labeled_file) = ordered_files.get_labeled_file(&target_info.file_path) {
        // Use OrderedFiles
        match construct_specific_edit_prompt(
            app_config, // Pass app_config
            &app_config.user_prompt,
            labeled_file,
            target_info,
            target_info.start_line == 0 && target_info.end_line == 0, // is_new_file flag
        ) {
            Ok(specific_prompt_text) => {
                let user_edit_prompt_message_for_api = ChatMessage {
                    // Renamed
                    role: ChatRole::User,
                    content: specific_prompt_text.clone(), // Clone here to allow reuse
                    message_type: MessageType::Text,
                };
                // DO NOT push to conversation_log or result_handler yet.

                let spinner_message = if target_info.start_line == 0 && target_info.end_line == 0 {
                    format!(
                        "Requesting content for new file {}...",
                        display_path.display()
                    )
                } else {
                    format!(
                        "Requesting edit for {} lines {}-{}...",
                        display_path.display(),
                        target_info.start_line,
                        target_info.end_line
                    )
                };
                // LLMSpinner removed
                // Log the spinner_message if needed, though TUI has its own indicators
                log::debug!("{}", spinner_message);

                // Construct messages for the API call
                let mut messages_for_llm_call = historical_context.to_vec();
                // conversation_log (messages_for_current_task) is already part of historical_context.
                // We only need to add the new user_edit_prompt_message_for_api.
                messages_for_llm_call.push(user_edit_prompt_message_for_api.clone());

                match chat_inference(llm_instance.as_ref(), &messages_for_llm_call).await {
                    // Pass as reference
                    // no_think removed
                    Ok(raw_code_response) => {
                        // spinner.finish() removed;

                        // Add the user message (sent to API) and assistant response to conversation_log
                        conversation_log.push(user_edit_prompt_message_for_api.clone());
                        // result_handler.add_chat_message(&user_edit_prompt_message_for_api); // Removed

                        log::trace!( // Level 3
                            "\n\n\nRaw LLM Response for specific code:\n<<<ResponseStart>>>\n{}\n<<<ResponseEnd>>>\n\n",
                            raw_code_response
                        );

                        let assistant_code_message = ChatMessage {
                            role: ChatRole::Assistant,
                            content: raw_code_response.clone(),
                            message_type: MessageType::Text,
                        };
                        conversation_log.push(assistant_code_message.clone());
                        // result_handler.add_chat_message(&assistant_code_message); // Removed

                        let expectations = vec![BlockExpectation {
                            parser: Box::new(ReplaceBlockParser),
                            expected_count: BlockCount::Exact(1),
                        }];

                        let mut temp_log_for_block_processing = Vec::new();
                        match process_llm_response_with_blocks(
                            &raw_code_response, // This is assistant_code_message.content
                            &expectations,
                            &specific_prompt_text, // Original prompt for this specific edit
                            &[labeled_file.clone()], // Context for the parser if needed
                            llm_instance.clone(),  // Clone Arc for this call
                            &messages_for_llm_call, // Use the history that was ACTUALLY sent to get raw_code_response
                            &mut temp_log_for_block_processing, // Pass the new, empty temporary log
                            app_config,
                        )
                        .await
                        {
                            Ok(processed_output) => {
                                let mut proposed_edit_option: Option<ProposedEdit> = None;
                                let mut final_delimiter_check_passed = true;

                                if let Some(replace_raw_blocks) = processed_output
                                    .successfully_parsed_blocks
                                    .get(&ReplaceBlockParser.id())
                                {
                                    if let Some(block) = replace_raw_blocks.first() {
                                        // This is safe because a block in successfully_parsed_blocks for ReplaceBlockParser
                                        // has already been validated and its `parse_to_string` is infallible.
                                        let parsed_code = ReplaceBlockParser
                                            .parse_to_string(block)
                                            .expect("Validated replace block should not fail parsing to string");
                                        {
                                            // Extract old code before creating the proposed edit
                                            let old_code = if target_info.start_line == 0 && target_info.end_line == 0 {
                                                // New file case
                                                String::new()
                                            } else {
                                                // Extract the lines that will be replaced
                                                let original_lines = labeled_file.get_original_lines();
                                                if target_info.start_line > 0 && target_info.start_line <= original_lines.len() {
                                                    let start_idx = target_info.start_line - 1; // Convert to 0-based
                                                    let end_idx = std::cmp::min(target_info.end_line, original_lines.len());
                                                    original_lines[start_idx..end_idx].join("\n")
                                                } else {
                                                    String::new()
                                                }
                                            };

                                            let mut current_proposed_edit = ProposedEdit {
                                                target: target_info.clone(),
                                                new_code: parsed_code,
                                                old_code,
                                            };
                                            let mut delimiter_check_passed = true;

                                            if target_info
                                                .file_path
                                                .extension()
                                                .map_or(false, |ext| ext == "rs")
                                                && app_config
                                                    .advanced_language_features
                                                    .contains("rust-delimiter-checking")
                                            {
                                                let original_code_slice = if target_info.start_line
                                                    == 0
                                                    && target_info.end_line == 0
                                                {
                                                    // is_new_file
                                                    String::new()
                                                } else {
                                                    let original_lines_vec =
                                                        labeled_file.get_original_lines();
                                                    let start_idx = (target_info.start_line - 1)
                                                        .min(original_lines_vec.len());
                                                    // For end_idx, if end_line is 0 (e.g. 0-0), this would be problematic.
                                                    // However, end_line should be >= start_line.
                                                    // If start_line is 1, end_line is 1, we want original_lines_vec[0..1] effectively.
                                                    // So end_idx for slicing should be target_info.end_line.
                                                    let end_idx_exclusive = (target_info.end_line)
                                                        .min(original_lines_vec.len());
                                                    if start_idx < end_idx_exclusive {
                                                        original_lines_vec
                                                            [start_idx..end_idx_exclusive]
                                                            .join("\n")
                                                    } else {
                                                        // Handles insertion points (e.g. target 5-4, or 5-5 on empty line 5)
                                                        // or empty ranges, where original slice is empty.
                                                        String::new()
                                                    }
                                                };

                                                match crate::language_integrations::rust::delimiter_fixer::check_and_fix_delimiters_for_edit(
                                                        &original_code_slice,
                                                        current_proposed_edit, // Pass ownership
                                                        app_config,
                                                        llm_instance.clone(), // Clone Arc
                                                        &messages_for_llm_call, // This is historical_context for the delimiter fixer
                                                        &target_info.file_path,
                                                        None, // No processing_tx available in target_processor
                                                    ).await {
                                                        crate::language_integrations::rust::delimiter_fixer::DelimiterCheckOutcome::Success(fixed_edit) => {
                                                            debug!("Delimiter check/fix successful for {}", display_path.display());
                                                            current_proposed_edit = fixed_edit;
                                                            delimiter_check_passed = true;
                                                        }
                                                        crate::language_integrations::rust::delimiter_fixer::DelimiterCheckOutcome::Failure(original_edit, err_msg) => {
                                                            warn!("Delimiter check/fix failed for {}: {}. Using original edit from LLM.", display_path.display(), err_msg);
                                                            current_proposed_edit = original_edit;
                                                            delimiter_check_passed = false;
                                                        }
                                                    }
                                            }

                                            if target_info.start_line == 0
                                                && target_info.end_line == 0
                                            {
                                                log::debug!(
                                                    "✅ LLM returned content for new file {}",
                                                    display_path.display()
                                                );
                                            } else {
                                                log::debug!(
                                                    "✅ LLM returned edit for {} lines {}-{}",
                                                    display_path.display(),
                                                    target_info.start_line,
                                                    target_info.end_line
                                                );
                                            }
                                            proposed_edit_option = Some(current_proposed_edit);
                                            final_delimiter_check_passed = delimiter_check_passed;
                                        }
                                    }
                                }

                                if !processed_output.remaining_raw_blocks.is_empty() {
                                    trace!("{} unclaimed raw blocks found in LLM response for target {}:", processed_output.remaining_raw_blocks.len(), display_path.display());
                                    for (i, block) in
                                        processed_output.remaining_raw_blocks.iter().enumerate()
                                    {
                                        trace!(
                                            "  Unclaimed block #{}: Keyword='{}', Content='{}...'",
                                            i + 1,
                                            block.keyword,
                                            block
                                                .content_after_keyword
                                                .chars()
                                                .take(50)
                                                .collect::<String>()
                                        );
                                    }
                                }
                                log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
                                Ok((proposed_edit_option, final_delimiter_check_passed))
                            }
                            Err(e) => {
                                log::trace!(
                                    "Error processing LLM response blocks for target {}: {}",
                                    display_path.display(),
                                    e
                                );
                                log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
                                Ok((None, false)) // Parsing error for this target, but not a critical failure for the whole cycle
                            }
                        }
                    }
                    Err(e) => {
                        // spinner.finish() removed;
                        let err_msg = format!(
                            "Error getting specific edit from LLM for {}: {}",
                            display_path.display(),
                            e
                        );
                        error!("{}", err_msg);
                        log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
                        Err(err_msg)
                    }
                }
            }
            Err(e) => {
                let err_msg = format!(
                    "Error constructing specific edit prompt for {}: {}",
                    display_path.display(),
                    e
                );
                error!("{}", err_msg);
                log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
                Err(err_msg)
            }
        }
    } else {
        let err_msg = format!(
            "Could not find file data for target path: {}",
            display_path.display()
        );
        error!("{}", err_msg);
        error!("Ensure LLM returns file paths exactly as provided in the initial prompt.");
        log::trace!("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"); // Level 3
        Err(err_msg)
    }
}
