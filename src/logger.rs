use log::LevelFilter; // Level, chrono::Local, colored, std::io::Write, EnvLoggerBuilder removed
use std::env;

#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq)]
pub enum LogLevel {
    Off,   // Level 0
    Info,  // Level 1 (maps to log::LevelFilter::Info)
    Debug, // Level 2 (maps to log::LevelFilter::Debug)
    Trace, // Level 3 (maps to log::LevelFilter::Trace)
}

// Convert LogLevel enum to log::LevelFilter
impl From<LogLevel> for LevelFilter {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Off => LevelFilter::Off,
            LogLevel::Info => LevelFilter::Info,
            LogLevel::Debug => LevelFilter::Debug,
            LogLevel::Trace => LevelFilter::Trace,
        }
    }
}

// Convert u8 to LogLevel (used by AppConfig)
impl From<u8> for LogLevel {
    fn from(value: u8) -> Self {
        match value {
            0 => LogLevel::Off,
            1 => LogLevel::Info,
            2 => LogLevel::Debug,
            3 => LogLevel::Trace,
            _ => {
                log::warn!("Invalid log level value '{}', defaulting to Info.", value);
                LogLevel::Info
            }
        }
    }
}

// Convert LogLevel to u8 (used for LLMSpinner if it expects u8)
impl From<LogLevel> for u8 {
    fn from(level: LogLevel) -> Self {
        match level {
            LogLevel::Off => 0,
            LogLevel::Info => 1,
            LogLevel::Debug => 2,
            LogLevel::Trace => 3,
        }
    }
}

pub fn init_logger(log_level: LogLevel, _timestamps_enabled: bool) {
    // Note: The TUI logger is now initialized in runner.rs via crate::tui::tui_logger::init()
    // This function is kept for compatibility but doesn't do anything in TUI mode
    let app_level_filter = LevelFilter::from(log_level);

    // Set the RUST_LOG environment variable for any other loggers that might be used
    let crate_name = env!("CARGO_PKG_NAME");
    let dep_general_filter = LevelFilter::Warn;

    let rust_log_directive = format!(
        "{crate_name}={app_level},hyper=off,reqwest=off,h2={dep_warn},rustls={dep_warn},mio={dep_warn}",
        app_level = app_level_filter.to_string().to_lowercase(),
        dep_warn = dep_general_filter.to_string().to_lowercase()
    );
    env::set_var("RUST_LOG", &rust_log_directive);
}
