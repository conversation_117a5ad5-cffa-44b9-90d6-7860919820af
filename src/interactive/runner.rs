use super::async_ops::clean_string_for_terminal;
use crate::api::types::APIMessage;
use crate::config::app_config::{load_data_from_results_input, save_message_history};
use crate::config::AppConfig;
use crate::editor::ProcessingSignal;

use crate::interactive::app::InteractiveApp;
use crate::interactive::app::LastSignificantCommandType;
use crate::interactive::app::{EditPlanItem, EditPlanItemStatus};
use crate::interactive::async_ops;
use crate::interactive::event::{EventHandler, TerminalEvent};
use crate::interactive::key_handler::handle_key_event;
use crate::interactive::ui::draw_ui;
use crate::notifications::{execute_notification_command, truncate_with_ellipsis};
use crate::result::Result as AppResult;
use crate::task::Task;
use crossterm::event::{<PERSON><PERSON><PERSON>, Mouse<PERSON>ventKind};
use crossterm::{
    event::{DisableMouse<PERSON>apture, Enable<PERSON>ouse<PERSON>apture, KeyC<PERSON>, KeyModifiers},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use log::{error, info};
use ratatui::{backend::CrosstermBackend, Terminal};
use std::collections::VecDeque;
use std::io::{self};
use std::path::PathBuf;
use std::time::Duration;
use tokio::sync::mpsc;


pub async fn run_interactive_mode(
    initial_app_config: &mut AppConfig, // Renamed to initial_app_config
    api_receiver: &mut mpsc::Receiver<APIMessage>,
    api_message_queue: &mut VecDeque<APIMessage>,
) -> Result<(AppResult, Option<LastSignificantCommandType>), Box<dyn std::error::Error>> {
    // Return (AppResult, Option<LastCommandType>)
    // --- Initial Setup ---
    let mut initial_tasks: Vec<Task> = Vec::new();
    let mut initial_qna_history: Vec<crate::interactive::app::QuestionAnswerPair> = Vec::new();
    let mut initial_input_history: Vec<String> = Vec::new();
    let mut result_handler = AppResult::new();
    let mut ordered_files = crate::files::ordered_files::OrderedFiles::new();

    if initial_app_config.restore_previous_session_on_startup {
        if let Err(e) = crate::config::load_and_apply_session(
            initial_app_config,
            &mut initial_tasks,
            &mut ordered_files,
            &mut initial_input_history,
        )
        .await
        {
            error!("Could not restore previous session: {}. Starting fresh.", e);
            // This is a critical error if it happens, so we return it.
            return Err(e);
        }
    } else {
        // --- Original loading logic if not restoring session ---
        // Load data from results input (replaces load_message_history)
        load_data_from_results_input(
            initial_app_config,
            &mut initial_tasks,
            &mut initial_qna_history,
        )
        .await?;

        if !initial_app_config.file_paths.is_empty() {
            for file_path_from_cli in &initial_app_config.file_paths {
                let path_to_add = file_path_from_cli.clone();
                // We add the path directly; add_path_recursively will handle canonicalization.
                if let Err(e) = ordered_files.add_path_recursively(&path_to_add).await {
                    error!("Error adding initial file {}: {}", path_to_add.display(), e);
                }
            }
        }
    }

    // Channel for async processing task to communicate back to TUI loop
    let (operation_signal_tx, mut operation_signal_rx) = mpsc::channel::<ProcessingSignal>(30); // Increased buffer further
                                                                                                // Channel for file search results
    let (file_search_results_tx, mut file_search_results_rx) = mpsc::channel::<Vec<PathBuf>>(50);

    // --- Terminal and App Initialization ---
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    let mut app = InteractiveApp::new(
        initial_tasks,
        initial_qna_history,
        initial_input_history,
        ordered_files,
        initial_app_config.user_prompt.clone(),
        initial_app_config.clone(),
    );
    let mut event_handler = EventHandler::new(Duration::from_millis(100));

    // Initialize the TUI logger
    let mut log_receiver = crate::tui::tui_logger::init();

    // Log startup information (Level 2)
    log::debug!("Started LLEdit In: \"{}\"", app.current_path.display());
    log::debug!("Provider: {}", app.app_config.provider);
    log::debug!("Default Model: {}", app.app_config.model);
    if !app.app_config.provider_url.is_empty() {
        log::debug!("Model Provider URL: {}", app.app_config.provider_url);
    } else {
        log::debug!("Model Provider URL: Default");
    }
    log::debug!("Auto-Research Mode: {}", app.app_config.auto_research_mode); // Log new mode
    if app.app_config.auto_test_toggle && !app.app_config.auto_test_command.is_empty() {
        log::debug!(
            "Auto-test command: \"{}\"",
            app.app_config.auto_test_command
        );
    }
    if !app.ordered_files.is_empty() {
        for (path_key, _) in app.ordered_files.get_all_labeled_files_map() {
            // path_key is canonical
            let display_path_for_initial_log = path_key
                .strip_prefix(&app.current_path)
                .map(|rel_path| PathBuf::from("./").join(rel_path))
                .unwrap_or_else(|_| path_key.clone());
            log::debug!(
                "Added file to context: {}",
                display_path_for_initial_log.display()
            ); // Initial load is debug
        }
        log::trace!("{}", "-".repeat(70)); // Level 3
    }
    log::debug!(
        "Lledit API Server URL: http://localhost:{}",
        app.app_config.http_api_port
    );
    if !app.app_config.advanced_language_features.is_empty() {
        log::debug!("Activated Advanced Language Features:");
        for feature_name in &app.app_config.advanced_language_features {
            log::debug!("  - {}", feature_name);
        }
        log::trace!("{}", "-".repeat(70));
    }

    // --- Handle Initial Prompt / Loaded Task ---

    // LLM client is no longer created here once for the session.
    // It will be created on-demand by async operations.

    // If app.input is not empty (e.g. from CLI -m) and interactive mode is on.
    if !app.input.is_empty() {
        let initial_cli_message = app.input.clone(); // Keep a copy of the original CLI message

        // Add to input history if it's new and not empty.
        // handle_command_if_present and submit_current_input will also try to add,
        // but this ensures it's added once, early.
        if !initial_cli_message.is_empty() && app.input_history.last() != Some(&initial_cli_message)
        {
            app.input_history.push(initial_cli_message.clone());
            app.current_history_index = None; // Reset history navigation
            app.current_input_draft.clear(); // Clear draft
        }

        // Reload files before processing any input to ensure freshness
        let _changed_files = app.ordered_files.refresh_all_files_from_filesystem().await;

        // Process the input as if "Enter" was pressed.
        // This will handle commands or treat it as a prompt.
        if crate::interactive::commands::handle_command_if_present(&mut app, &operation_signal_tx)
            .await
        {
            // Command was handled by handle_command_if_present.
            // app.input is cleared by handle_command_if_present.
            // If it was /quit, app.should_quit will be true.
        } else {
            // Not a command, or an unknown command that wasn't fully processed.
            // Treat app.input (which still holds initial_cli_message) as a user prompt.
            app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Set for edit task
                                                                                            // Note: handle_command_if_present clears app.input if it's a known command.
            if app.input == initial_cli_message {
                app.task_retry_count = 0;

                // submit_current_input uses app.input, sets app.is_processing, app_config.user_prompt, logs, sends notification.
                // It also adds to history, which is fine if it's a duplicate add, history Vec handles it.
                // submit_current_input also sets original_user_request_for_current_processing_sequence.
                app.submit_current_input();
                // If submit_current_input wasn't called because app.input was empty after a command,
                // ensure original_user_request_for_current_processing_sequence is set if we proceed with initial CLI message.
                // However, submit_current_input IS called here.

                // Now that app.is_processing is true and app_config.user_prompt is set, create the task.
                let mut new_task_for_user_input = Task::new(
                    app.app_config.user_prompt.clone(), // Use the prompt set by submit_current_input
                    app.app_config.task_info.clone(),
                );

                let (files_msg_opt, _included_content) = app // _included_content not stored on Task
                    .ordered_files
                    .get_files_for_prompt_message(&app.current_path);
                if let Some(files_msg) = files_msg_opt {
                    new_task_for_user_input.add_message(files_msg);
                }
                app.tasks.push(new_task_for_user_input.clone());

                // Get previous tasks for run_edit_cycle
                let all_previous_tasks_for_processing: Vec<Task> = app
                    .tasks
                    .iter()
                    .take(app.tasks.len().saturating_sub(1)) // All except the one just added
                    .cloned()
                    .collect();

                let task_app_config_user = app.app_config.clone();
                let task_ordered_files_snapshot_user = app.ordered_files.clone(); // Clone OrderedFiles

                // Clear input field for the TUI after processing the initial CLI message.
                app.input.clear();
                app.input_cursor_char_idx = 0;

                // Similar to key_handler, new_task_for_user_input is already in app.tasks.
                // Its messages field contains the initial messages for this task.
                let initial_messages_for_cli_task = new_task_for_user_input.messages.clone();

                // Set model alias for edit processing
                app.current_operation_model_alias = Some(app.app_config.default_model.clone());

                let abort_handle = async_ops::spawn_new_edit_processing(
                    task_app_config_user,
                    new_task_for_user_input.clone(), // Pass a clone of the task object
                    app.pruned_tasks_summary_messages.clone(),
                    all_previous_tasks_for_processing,
                    initial_messages_for_cli_task, // Pass the specifically prepared initial messages
                    app.ordered_files.clone(),
                    app.current_path.clone(),
                    operation_signal_tx.clone(),
                    0,    // is_retry
                    None, // forced_research_content
                );
                app.current_task_abort_handle = Some(abort_handle);
            }
        }
    } else if !app.tasks.is_empty() && !initial_app_config.user_prompt.is_empty() {
        // This case handles when history is loaded AND a CLI prompt was also given.
        // The CLI prompt is in app.input. If it wasn't processed above (e.g. because app.input was empty initially),
        // it might mean the initial_app_config.user_prompt was from a non-interactive run setup.
        // For interactive mode, if app.input was populated by CLI -m, it's handled above.
        // If app.input was empty, but initial_app_config.user_prompt had something (e.g. from a previous non-interactive default),
        // we just log the loaded history's last prompt.
        if let Some(last_task) = app.tasks.last() {
            app.app_config.user_prompt = last_task.initial_user_prompt.clone();
            log::info!(
                "Loaded {} tasks from history. Last prompt: \"{}\"",
                app.tasks.len(),
                app.app_config
                    .user_prompt
                    .chars()
                    .take(50)
                    .collect::<String>()
            );
        }
    }
    // Note: The old `task_to_process_immediately` and the second `if app.is_processing && task_to_process_immediately.is_some()` block
    // are now effectively handled by the logic above, which directly spawns processing if needed.

    // --- Main TUI Loop ---
    loop {
        terminal.draw(|f| draw_ui(f, &mut app))?;

        tokio::select! {
            biased; // Prioritize event_handler.next() to keep UI responsive.

            event_opt = event_handler.next() => {
                match event_opt { // event_opt is Option<TerminalEvent>
                    Some(TerminalEvent::KeyPress(key_event)) => {
                        // Global key handling for KeyPress
                        match key_event {
                            // Ctrl+Q to quit
                            KeyEvent { code: KeyCode::Char('q'), modifiers: KeyModifiers::CONTROL, .. } => {
                                        app.should_quit = true;
                                    }
                                    // Log scrolling keys (now active even during processing)
                                    KeyEvent { code: KeyCode::PageUp, modifiers: KeyModifiers::NONE, .. } => {
                                        app.logger_state.page_up();
                                    }
                                    // Ctrl+U for Page Up
                                    KeyEvent { code: KeyCode::Char('u'), modifiers: KeyModifiers::CONTROL, .. } => {
                                        app.logger_state.page_up();
                                    }
                                    // Ctrl+F for Page Up
                                    KeyEvent { code: KeyCode::Char('f'), modifiers: KeyModifiers::CONTROL, .. } => {
                                        app.logger_state.page_up();
                                    }
                                    KeyEvent { code: KeyCode::PageDown, modifiers: KeyModifiers::NONE, .. } => {
                                        app.logger_state.page_down();
                                    }
                                    // Ctrl+D for Page Down
                                    KeyEvent { code: KeyCode::Char('d'), modifiers: KeyModifiers::CONTROL, .. } => {
                                        app.logger_state.page_down();
                                    }
                                    // Esc key handling (global for non-processing states)
                                    KeyEvent { code: KeyCode::Esc, modifiers: KeyModifiers::NONE, .. } if !app.is_processing && !app.is_researching => {
                                        if app.file_suggester_state.active {
                                            app.file_suggester_state.deactivate();
                                        } else if app.autocomplete_state.active {
                                            app.autocomplete_state.deactivate();
                                        } else if app.model_suggester_state.active {
                                            app.model_suggester_state.deactivate();
                                        } else if app.input_mode == crate::interactive::app::InputMode::FilesFocus {
                                            app.input_mode = crate::interactive::app::InputMode::Normal;
                                            app.focused_file_index = None;
                                        } else if app.input_mode == crate::interactive::app::InputMode::AwaitingRunConfirmation {
                                            app.last_run_command_output.take();
                                            app.last_run_command_executed.take();
                                            log::debug!("Run confirmation cancelled. Output will not be added to task info.");
                                            app.input_mode = crate::interactive::app::InputMode::Normal;
                                            app.input.clear();
                                            app.input_cursor_char_idx = 0;
                                        }
                                    }
                                    // Ctrl+C to cancel processing, research, or question
                                    KeyEvent { code: KeyCode::Char('c'), modifiers: KeyModifiers::CONTROL, .. } => {
                                        if app.is_processing {
                                            if let Some(handle) = app.current_task_abort_handle.take() {
                                                handle.abort();
                                                app.is_processing = false;
                                                app.clear_editing_plan(); // Clear plan on cancel
                                                app.input_mode = crate::interactive::app::InputMode::Normal;
                                                app.focused_file_index = None;
                                                app.processing_progress = 0.0;
                                                app.show_success_state_until = None;
                                                app.task_retry_count = 0;
                                                app.is_auto_researching = false;
                                                // If the current task has no edits applied, remove it. Otherwise, mark as cancelled.
                                                // If the current task has no edits applied, remove it. Otherwise, mark as cancelled.
                                                if let Some(task) = app.tasks.last().cloned() {
                                                    if task.edited_files_paths.is_empty() {
                                                        app.tasks.pop();
                                                        log::debug!("Task cancelled before edits applied. Removed from history.");
                                                    } else {
                                                        if let Some(task_mut) = app.tasks.last_mut() {
                                                            task_mut.add_message(crate::llm::ChatMessage {
                                                                role: crate::llm::ChatRole::System,
                                                                content: "Edit task cancelled by user after edits were applied.".to_string(),
                                                                message_type: crate::llm::MessageType::Text,
                                                            });
                                                            log::debug!("Task cancelled after edits applied. Kept in history with cancellation message.");
                                                        }
                                                        app.last_completed_task = Some(task);
                                                    }
                                                }
                                            }
                                        } else if app.is_researching {
                                            if let Some(handle) = app.current_research_abort_handle.take() {
                                                handle.abort();
                                                app.is_researching = false;
                                                // Research tasks don't apply edits, so remove them on cancel.
                                                if let Some(task_id_to_remove) = app.current_research_task_id.take() {
                                                    app.tasks.retain(|t| t.id != task_id_to_remove);
                                                    log::debug!("Research task '{}' cancelled and removed from history.", task_id_to_remove);
                                                }
                                            }
                                        } else if app.is_asking_question {
                                            if let Some(handle) = app.current_task_abort_handle.take() {
                                                handle.abort();
                                                app.is_asking_question = false;
                                            }
                                        }
                                    }
                                    key_event_to_handle => {
                                        if !app.is_processing && !app.is_researching && !app.is_asking_question {
                                            handle_key_event(
                                                &mut app,
                                                key_event_to_handle,
                                                &file_search_results_tx,
                                                &operation_signal_tx,
                                            ).await;
                                        }
                                        // If app.is_processing or app.is_researching, other keys are ignored by this block.
                                        // Log scrolling is handled globally above.
                                                    }
                                                }
                                            }
                                    Some(TerminalEvent::MouseEvent(mouse_event)) => {
                                        // Global mouse handling
                                        match mouse_event.kind {
                                            MouseEventKind::ScrollUp => {
                                                app.logger_state.scroll_up(3); // Scroll 3 lines at a time for mouse
                                            }
                                            MouseEventKind::ScrollDown => {
                                                app.logger_state.scroll_down(3); // Scroll 3 lines at a time for mouse
                                            }
                                            _ => {} // Other mouse events (click, drag) are ignored
                                        }
                                    }
                                    // The following arms are siblings to `Some(TerminalEvent::KeyPress(key_event))`
                                    Some(TerminalEvent::Tick) => {
                        // Update typewriter animation
                        app.logger_state.update_typewriter();

                        // Check for API messages
                        while let Ok(message) = api_receiver.try_recv() {
                            api_message_queue.push_back(message);
                        }

                        // If the app is idle, process a message from the queue
                        if !app.is_processing() && !api_message_queue.is_empty() {
                            if let Some(message) = api_message_queue.pop_front() {
                                match message {
                                    APIMessage::SubmitInput(input) => {
                                        // The input from the API is now being processed.
                                        // It will be visible in the TUI input box briefly while being handled.
                                        app.handle_input(input, &operation_signal_tx).await;
                                        // After handling, clear the input box from the TUI.
                                        app.input.clear();
                                        app.input_cursor_char_idx = 0;
                                    }
                                }
                            }
                        }

                        if app.is_processing || app.is_researching || app.is_asking_question { // Update spinner if any operation is active
                            app.spinner_index = (app.spinner_index + 1) % app.spinner_chars.len();
                        }
                        if let Some(expiry_time) = app.show_success_state_until {
                            if std::time::Instant::now() >= expiry_time {
                                app.show_success_state_until = None;
                                app.processing_progress = 0.0; // Reset for inactive state
                                app.clear_editing_plan(); // Clear plan when success display ends
                            }
                        }
                    }
                    None => { // The event_handler channel was closed
                        log::error!("Terminal event channel closed. Exiting interactive mode.");
                        app.should_quit = true;
                    }
                }
            },
            search_result_opt = file_search_results_rx.recv() => {
                match search_result_opt {
                    Some(found_paths_batch) => {
                        if app.file_suggester_state.active {
                            if found_paths_batch.is_empty() && app.file_suggester_state.suggestions.is_empty() {
                                // No action needed, set_found_paths handles empty batches.
                            }
                            app.file_suggester_state.set_found_paths(found_paths_batch, &app.current_path);
                        }
                    }
                    None => { // File search channel closed
                        log::debug!("File search results channel closed.");
                        // This is not necessarily a reason to quit the app.
                        // File suggester will stop receiving updates.
                    }
                }
            },
            // Listen for log messages from the TUI logger
            log_message_opt = log_receiver.recv() => {
                match log_message_opt {
                    Some(log_message) => {
                        app.logger_state.add_message(log_message);
                    }
                    None => {
                        log::debug!("Log message channel closed.");
                    }
                }
            },
            // Listen for messages from the processing or research task
            signal_opt = operation_signal_rx.recv() => {
                match signal_opt {
                    Some(signal) => {
                        match signal {
                            ProcessingSignal::UpdateProgress(new_progress) => {
                                if app.is_processing { // Only update if in edit processing mode
                                    app.processing_progress = new_progress.clamp(0.0, 1.0);
                                } else if app.is_researching { // Or if in research mode
                                    app.research_progress = new_progress.clamp(0.0, 1.0);
                                }
                            }
                            ProcessingSignal::EditPlanReady { plan } => {
                                app.editing_plan_items = plan.iter().map(|target| {
                                    let filename = target.file_path.file_name()
                                        .map_or_else(|| target.file_path.to_string_lossy().into_owned(), |name| name.to_string_lossy().into_owned());
                                    EditPlanItem {
                                        path: target.file_path.clone(),
                                        filename,
                                        start_line: target.start_line,
                                        end_line: target.end_line,
                                        status: EditPlanItemStatus::Pending,
                                    }
                                }).collect();
                                app.current_processing_target_index = None;
                                app.total_targets_for_progress = app.editing_plan_items.len(); // Update total targets
                            }
                            ProcessingSignal::TargetProcessingStart { target_index } => {
                                if let Some(item) = app.editing_plan_items.get_mut(target_index) {
                                    item.status = EditPlanItemStatus::Processing;
                                }
                                app.current_processing_target_index = Some(target_index);
                            }
                            ProcessingSignal::TargetProcessed { target_index, success } => {
                                if let Some(item) = app.editing_plan_items.get_mut(target_index) {
                                    item.status = if success { EditPlanItemStatus::Success } else { EditPlanItemStatus::Failure };
                                }
                                // Optionally clear current_processing_target_index or keep it to show last processed
                                // For now, let's keep it, the UI will show the check/cross.
                            }
                            ProcessingSignal::AutoTestReprompting { new_task_for_reprompt, abort_handle } => {
                                app.is_processing = true;
                                app.is_researching = false;
                                app.clear_editing_plan();
                                app.input_mode = crate::interactive::app::InputMode::Normal;
                                app.tasks.push(new_task_for_reprompt.clone());
                                app.app_config.user_prompt = new_task_for_reprompt.initial_user_prompt.clone(); // This is the user-facing prompt for the retry
                                app.app_config.task_info = new_task_for_reprompt.initial_task_info.clone(); // Task info for the retry
                                app.app_config.forced_research_for_next_cycle = None;

                                // Prepare initial messages for the reprompt task
                                // Prepare initial messages for the reprompt task
                                let mut initial_messages_for_reprompt_task: Vec<crate::llm::ChatMessage> =
                                    Vec::new();
                                // The pruned summaries will be passed to spawn_new_edit_processing separately.
                                // This vector is for the *new* user instruction for this specific retry.
                                let mut reprompt_instruction_content = new_task_for_reprompt.initial_user_prompt.clone();
                                if let Some(ti_content) = &new_task_for_reprompt.initial_task_info {
                                    if !ti_content.trim().is_empty() {
                                        reprompt_instruction_content = format!(
                                            "Important task context:\n---\n{}\n---\n\n{}",
                                            ti_content.trim(),
                                            reprompt_instruction_content
                                        );
                                    }
                                }
                                initial_messages_for_reprompt_task.push(crate::llm::ChatMessage {
                                    role: crate::llm::ChatRole::User,
                                    content: reprompt_instruction_content,
                                    message_type: crate::llm::MessageType::Text,
                                });

                                // The new_task_for_reprompt object itself is added to app.tasks by the signal handler.
                                // We need to ensure spawn_new_task_processing gets the correct arguments.
                                // The `abort_handle` received here is for the *new* processing task.
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reprompt uses default edit model
                                app.current_task_abort_handle = Some(abort_handle); // Store the new abort handle
                                app.current_research_abort_handle = None; // Clear any old research handle
                                app.input.clear(); // Clear user input field
                                app.input_cursor_char_idx = 0;
                                app.processing_progress = 0.0;
                                app.research_progress = 0.0;
                                app.show_success_state_until = None;
                                // current_task_start_time for the failed attempt is taken by log_attempt_completion_metrics.
                                // task_retry_count is incremented before logging.
                                app.task_retry_count += 1;
                                log_attempt_completion_metrics(&mut app, "Editing Round");

                                app.current_task_start_time = Some(std::time::Instant::now()); // For the new reprompt attempt
                            }
                            ProcessingSignal::AutoTestPassed => {
                                // Log metrics for the successful attempt.
                                // current_task_start_time is taken by log_attempt_completion_metrics.
                                let _ = log_attempt_completion_metrics(&mut app, "Editing Round");

                                // Log final total duration for the entire sequence and update result_handler.
                                if let Some(original_start_time) = app.original_task_start_time.take() {
                                    let total_duration = original_start_time.elapsed();
                                    result_handler.total_duration_ms_for_last_edit_task = Some(total_duration.as_millis());
                                }
                                result_handler.total_retries_for_last_edit_task = Some(app.task_retry_count);
                                app.task_retry_count = 0; // Reset on pass

                                app.is_processing = false; // Auto-test passed, sequence complete.
                                app.is_auto_researching = false;
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reset to default
                                app.processing_progress = 1.0;
                                app.show_success_state_until = Some(std::time::Instant::now() + std::time::Duration::from_secs(3));
                                app.current_task_abort_handle = None;
                                app.current_processing_target_index = None;


                                 // Add the successful task to result_handler and set as last_completed_task
                                 if let Some(task) = app.tasks.last().cloned() {
                                     result_handler.add_task(&task);
                                     app.last_completed_task = Some(task);
                                 }

                                if app.app_config.exit_on_success {
                                    app.should_quit = true;
                                }
                            }
                            ProcessingSignal::AutoResearchStarted => {
                                app.is_auto_researching = true;
                                // Set model alias based on whether decision or research model is used for this phase
                                // This signal means the auto-research *process* (which might include an LLM decision then research) has started.
                                // The actual research sub-cycle will use research_model.
                                // The decision part uses decision_model_alias.
                                // For simplicity, let's assume this signal means the *decision* part is active or about to be.
                                app.current_operation_model_alias = Some(
                                    app.app_config.decision_model.as_ref()
                                        .filter(|&a| a.to_lowercase() != "none" && a.to_lowercase() != "default")
                                        .cloned()
                                        .unwrap_or_else(|| app.app_config.default_model.clone())
                                );
                                app.research_bash_commands_issued = 0; // Reset for auto-research phase
                            }
                            ProcessingSignal::AutoResearchEnded => {
                                app.is_auto_researching = false;
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reset to default or next phase model
                            }
                            ProcessingSignal::AutoResearchUpdate { categorized_files } => {
                                if app.is_auto_researching { // Only process if auto-research is the active phase
                                    // Update model alias to reflect the research model used for this update
                                    app.current_operation_model_alias = Some(
                                        app.app_config.research_model.as_ref()
                                            .filter(|&a| a.to_lowercase() != "none" && a.to_lowercase() != "default")
                                            .cloned()
                                            .unwrap_or_else(|| app.app_config.default_model.clone())
                                    );
                                    // Add newly discovered files to the main app.ordered_files state
                                    let add_results_map = app.ordered_files.add_categorized_files_from_research(&categorized_files).await;
                                    for (path, result) in add_results_map {
                                        if let Err(e) = result {
                                            log::error!("Failed to add file {} from auto-research update to OrderedFiles: {}", path.display(), e);
                                        }
                                    }
                                    // Apply categorization to all files (new and existing)
                                    app.ordered_files.apply_research_categorization(&categorized_files);
                                    // The TUI will redraw and pick up changes from app.ordered_files.
                                } else {
                                    log::warn!("AutoResearchUpdate received but app is not in auto-researching state. Ignoring.");
                                }
                            }
                            // ProcessingSignal::AutoResearchFilesDiscovered removed.
                            ProcessingSignal::ProcessingComplete(result) => {
                                // This signal means the task processing (edit cycle) has finished.
                                // app.is_processing is NOT set to false here if auto-test is pending.
                                app.is_auto_researching = false;
                                app.is_asking_question = false;
                                app.current_task_abort_handle = None; // Abort handle for the edit cycle is now done.
                                app.current_processing_target_index = None;

                                match result {
                                    Ok(success_data) => {
                                        // Update app.tasks with results from success_data
                                        let mut current_task_id_for_edits: Option<String> = None;
                                        let mut applied_edit_paths_this_run_option: Option<std::collections::HashSet<PathBuf>> = None;

                                        if !success_data.proposed_edits.is_empty() {
                                            let mut edited_paths_set_for_task = std::collections::HashSet::new();
                                            for edit in &success_data.proposed_edits {
                                                edited_paths_set_for_task.insert(edit.target.file_path.clone());
                                            }
                                            applied_edit_paths_this_run_option = Some(edited_paths_set_for_task);
                                        }

                                        // Convert proposed edits to applied edits (old_code flows naturally from ProposedEdit)
                                        let applied_edits_with_old_code = if !success_data.proposed_edits.is_empty() {
                                            success_data
                                                .proposed_edits
                                                .iter()
                                                .map(|pe| {
                                                    crate::task::AppliedEdit {
                                                        file_path: pe.target.file_path.clone(),
                                                        start_line: pe.target.start_line,
                                                        end_line: pe.target.end_line,
                                                        new_code: pe.new_code.clone(),
                                                        old_code: pe.old_code.clone(),
                                                    }
                                                })
                                                .collect::<Vec<_>>()
                                        } else {
                                            Vec::new()
                                        };

                                        if let Some(current_task_ref) = app.current_task_mut() {
                                            current_task_ref.messages = success_data.updated_messages_for_current_task.clone();
                                            if let Some(summary_text) = &success_data.task_summary {
                                                current_task_ref.summary = Some(summary_text.clone());
                                            }
                                            if !success_data.proposed_edits.is_empty() {
                                                current_task_id_for_edits = Some(current_task_ref.id.clone());
                                                if let Some(ref paths_set) =
                                                    applied_edit_paths_this_run_option
                                                {
                                                    current_task_ref.edited_files_paths =
                                                        paths_set.clone();
                                                }
                                                // Use the pre-captured applied_edits with old_code
                                                current_task_ref.applied_edits = applied_edits_with_old_code;
                                            }
                                        }
                                        app.app_config.task_info = None; // Clear task_info after successful processing of one cycle part

                                        // Apply edits to files and update app.labeled_files_map
                                        let mut apply_error_message_option: Option<String> = None;

                                        // Define a local struct for detailed edit information
                                        #[derive(Clone)]
                                        struct FullEditDetail {
                                            path: PathBuf,
                                            start_line: usize,
                                            end_line: usize,
                                            before_code: String,
                                            after_code: String,
                                        }
                                        let mut full_edit_details_for_autotest: Vec<FullEditDetail> = Vec::new();

                                        if !success_data.proposed_edits.is_empty() {
                                            // Capture edit details using old_code from ProposedEdit
                                            for edit in &success_data.proposed_edits {
                                                full_edit_details_for_autotest.push(FullEditDetail {
                                                    path: edit.target.file_path.clone(),
                                                    start_line: edit.target.start_line,
                                                    end_line: edit.target.end_line,
                                                    before_code: edit.old_code.clone(), // Use old_code from ProposedEdit
                                                    after_code: edit.new_code.clone(),
                                                });
                                            }

                                            match crate::editor::apply_and_write_edits_interactive(
                                                &app.app_config,
                                                &success_data.proposed_edits,
                                                &mut app.ordered_files, // Pass &mut app.ordered_files
                                                &mut result_handler,
                                            ).await {
                                                Ok(_) => { /* Edits applied, success logged by apply_and_write_edits_interactive */ }
                                                Err(e) => {
                                                    let err_msg = format!("Failed to apply edits: {}", e);
                                                    log::error!("{}", err_msg);
                                                    apply_error_message_option = Some(err_msg);
                                                }
                                            }
                                        }
                                        // Log apply error to task messages if any
                                        if let Some(task_id) = current_task_id_for_edits {
                                            if let Some(task_to_update) = app.tasks.iter_mut().find(|t| t.id == task_id) {
                                                if let Some(err_msg_content) = apply_error_message_option {
                                                    task_to_update.add_message(crate::llm::ChatMessage {
                                                        role: crate::llm::ChatRole::System,
                                                        content: err_msg_content,
                                                        message_type: crate::llm::MessageType::Text,
                                                    });
                                                }
                                            }
                                        }

                                        // Log "Process Complete"
                                        let num_edits_applied = success_data.proposed_edits.len();
                                        let num_files_edited = applied_edit_paths_this_run_option.as_ref().map_or(0, |s| s.len());
                                        let edit_summary_for_log = if num_edits_applied > 0 {
                                            format!("Applied {} edits to {} files.", num_edits_applied, num_files_edited)
                                        } else {
                                            success_data.summary_message.clone() // Use LLM's summary if no edits
                                        };
                                        log::info!("Process Complete: {}", edit_summary_for_log);

                                        // --- History Pruning ---
                                         // --- History Pruning ---
                                         // Pruning now happens unconditionally if there are tasks.
                                         let (
                                             updated_tasks_after_pruning,
                                             updated_app_config_after_pruning,
                                             newly_pruned_messages,
                                         ) = crate::history_pruner::prune_history_if_needed(
                                             app.tasks.clone(), // Pass current tasks
                                             app.app_config.clone(), // Pass current app_config
                                         );
                                         app.tasks = updated_tasks_after_pruning; // Update app's tasks
                                         app.app_config = updated_app_config_after_pruning; // Update app's config
                                         app.pruned_tasks_summary_messages
                                             .extend(newly_pruned_messages); // Append new summaries
                                                                             // --- End History Pruning ---

                                        // Update app.ordered_files with new files and categorization from research
                                        if let Some(new_cat_from_signal) = &success_data.new_categorization {
                                            // Add newly discovered files to the main app.ordered_files state
                                            let add_results_map = app.ordered_files.add_categorized_files_from_research(new_cat_from_signal).await;
                                            for (path, result) in add_results_map {
                                                if let Err(e) = result {
                                                    log::error!("Failed to add file {} from research to OrderedFiles: {}", path.display(), e);
                                                    // Decide if this is a critical error or just log and continue
                                                }
                                            }
                                            // Apply categorization to all files (new and existing)
                                            app.ordered_files.apply_research_categorization(new_cat_from_signal);
                                        }


                                        // If auto-test is enabled, app.is_processing might remain true or a new task is spawned.
                                        let should_run_auto_test = app.app_config.auto_test_toggle && !app.app_config.auto_test_command.is_empty();
                                        // let has_pending_research = success_data.pending_research_content.is_some(); // Removed

                                        if should_run_auto_test {
                                            app.app_config.forced_research_for_next_cycle = None; // Research is handled within cycle, not carried to auto-test
                                            // app.is_processing remains true.
                                            log::debug!("{}", "-".repeat(70));
                                            log::info!("Auto-test enabled. Running command: `{}`", app.app_config.auto_test_command);
                                            // ... (auto-test spawning logic as before, but app_config_for_test_async will now have forced_research_for_next_cycle set if needed)
                                            // The spawn_new_task_processing call for re-prompt will use this.
                                            let auto_test_cmd_str = app.app_config.auto_test_command.clone();
                                            let current_path_for_blocking_task = app.current_path.clone();
                                            let current_path_for_test_async = app.current_path.clone();
                                                 let _completed_task_prompt_for_reprompt_fallback = app.app_config.user_prompt.clone();
                                             let _original_user_request_for_sequence_captured = app
                                                 .original_user_request_for_current_processing_sequence
                                                 .clone();
                                            let captured_pruned_summaries =
                                                app.pruned_tasks_summary_messages.clone();
                                            let operation_signal_tx_for_autotest_async =
                                                operation_signal_tx.clone();
                                            let mut app_config_for_test_async = app.app_config.clone();
                                            app_config_for_test_async.forced_research_for_next_cycle = None; // No pending research for auto-test reprompt

                                            let captured_full_edit_details = full_edit_details_for_autotest.clone(); // Clone for the async block

                                            let ordered_files_for_test_async = app.ordered_files.clone(); // Clone OrderedFiles
                                            // categorization_for_reprompt is no longer directly available from app state.
                                            // generate_files_message_info will use app.ordered_files.
                                            // For now, pass None. If specific categorization is needed for reprompt,
                                            // it would need to be derived or passed differently.
                                            let _categorization_for_reprompt: Option<crate::research::types::CategorizedFilePaths> = None; // Prefixed as unused
                                            let current_task_id_option = app.tasks.last().map(|ct| ct.id.clone());
                                            let all_previous_tasks_for_reprompt: Vec<Task> = app.tasks.iter()
                                                .filter(|t| Some(&t.id) != current_task_id_option.as_ref())
                                                .cloned()
                                                .collect();

                                            tokio::spawn(async move {
                                                let command_output_result = tokio::task::spawn_blocking(move || {
                                                    if auto_test_cmd_str.is_empty() { return Err(std::io::Error::new(std::io::ErrorKind::InvalidInput, "No command provided for auto-test")); }
                                                    std::process::Command::new("sh").arg("-c").arg(&auto_test_cmd_str).current_dir(&current_path_for_blocking_task).output()
                                                }).await;
                                                match command_output_result {
                                                    Ok(Ok(output)) => {
                                                        if !output.status.success() {
                                                            let fail_msg_for_log = format!("❌ Auto-Test FAILED: `{}` (Exit Code: {:?})", app_config_for_test_async.auto_test_command, output.status.code());
                                                            log::warn!("{}", fail_msg_for_log);
                                                            execute_notification_command(&app_config_for_test_async.notification_command, &current_path_for_test_async, "❌ LLEdit Auto-test Failed", "Auto-test failed, re-prompting LLM.");
                                                            let stdout_str_raw = String::from_utf8_lossy(&output.stdout);
                                                            let stderr_str_raw = String::from_utf8_lossy(&output.stderr);
                                                            let stdout_str = clean_string_for_terminal(&stdout_str_raw);
                                                            let stderr_str = clean_string_for_terminal(&stderr_str_raw);
                                                            let combined_output = if !stdout_str.is_empty() && !stderr_str.is_empty() { format!("--- STDOUT ---\n{}\n--- STDERR ---\n{}", stdout_str.trim_end(), stderr_str.trim_end()) } else if !stdout_str.is_empty() { stdout_str.trim_end().to_string() } else if !stderr_str.is_empty() { format!("--- STDERR ---\n{}", stderr_str.trim_end()) } else { "<no output>".to_string() };
                                                            log::debug!("Auto-test output:\n{}", combined_output);
                                                            let mut app_config_for_reprompt = app_config_for_test_async.clone(); // This config will be used by spawn_new_task_processing

                                                            let mut diagnostic_details = String::new();
                                                            diagnostic_details.push_str("--- Auto-Test Failure Analysis ---\n");
                                                            diagnostic_details.push_str("The following files were edited in the attempt that failed tests:\n");
                                                            for detail in &captured_full_edit_details {
                                                                let display_path = detail.path.strip_prefix(&current_path_for_test_async).unwrap_or(&detail.path);
                                                                if detail.start_line == 0 && detail.end_line == 0 {
                                                                    diagnostic_details.push_str(&format!("- {} (New File)\n", display_path.display()));
                                                                } else {
                                                                    diagnostic_details.push_str(&format!("- {} (lines {}-{})\n", display_path.display(), detail.start_line, detail.end_line));
                                                                }
                                                            }
                                                            diagnostic_details.push_str("\nDetailed changes in files potentially related to test failures:\n");
                                                            for detail in &captured_full_edit_details {
                                                                if let Some(filename_osstr) = detail.path.file_name() {
                                                                    let filename_str = filename_osstr.to_string_lossy();
                                                                    if combined_output.contains(&*filename_str) {
                                                                        let display_path = detail.path.strip_prefix(&current_path_for_test_async).unwrap_or(&detail.path);
                                                                        diagnostic_details.push_str(&format!("\nFile: {} (lines {}-{})\n", display_path.display(), detail.start_line, detail.end_line));
                                                                        diagnostic_details.push_str(&format!("This file ({}) was mentioned in the test output.\n", filename_str));
                                                                        diagnostic_details.push_str("Before:\n");
                                                                        diagnostic_details.push_str(&detail.before_code);
                                                                        diagnostic_details.push_str("\nAfter:\n");
                                                                        diagnostic_details.push_str(&detail.after_code);
                                                                        diagnostic_details.push_str("\n");
                                                                    }
                                                                }
                                                            }
                                                            diagnostic_details.push_str("\n--- Test Command Output ---\n");
                                                            diagnostic_details.push_str(&combined_output);

                                                            let auto_test_failure_info = diagnostic_details;

                                                            // The new_task_for_reprompt object will be created with the updated prompt and task_info
                                                            let reprompt_user_facing_prompt = format!("\nThe previous edits resulted in errors when running the test command: '{}'. Please address these errors while fulfilling the original request. If you keep running into the same issue, remember you always have the option to rewrite the whole file by specifying its whole range.", app_config_for_reprompt.auto_test_command);
                                                            let reprompt_task_info = match app_config_for_reprompt.task_info.take() { Some(existing_info) if !existing_info.trim().is_empty() => Some(format!("{}\n\n{}", existing_info, auto_test_failure_info)), _ => Some(auto_test_failure_info), };

                                                            let new_reprompt_task_object = Task::new(reprompt_user_facing_prompt.clone(), reprompt_task_info.clone());

                                                            // Prepare initial messages for the reprompt task (excluding files message, which is handled by history_assembler)
                                                            let mut initial_messages_for_reprompt: Vec<crate::llm::ChatMessage> = Vec::new();
                                                            // Pruned summary for the reprompt task will be handled by the main loop when it receives AutoTestReprompting signal.

                                                            let mut reprompt_instruction_for_llm = reprompt_user_facing_prompt;
                                                            if let Some(ti_content) = &reprompt_task_info {
                                                                if !ti_content.trim().is_empty() {
                                                                    reprompt_instruction_for_llm = format!(
                                                                        "Important task context:\n---\n{}\n---\n\n{}",
                                                                        ti_content.trim(),
                                                                        reprompt_instruction_for_llm
                                                                    );
                                                                }
                                                            }
                                                            initial_messages_for_reprompt.push(crate::llm::ChatMessage {
                                                                role: crate::llm::ChatRole::User,
                                                                content: reprompt_instruction_for_llm,
                                                                message_type: crate::llm::MessageType::Text,
                                                            });

                                                            log::info!("Auto-test failed. Re-prompting LLM to fix errors."); log::debug!("{}", "-".repeat(70));

                                                            // Spawn new processing for the reprompt
                                                             let reprompt_abort_handle =
                                                                 async_ops::spawn_new_edit_processing(
                                                                     app_config_for_reprompt, // Pass the config with updated prompt/task_info
                                                                     new_reprompt_task_object.clone(), // The Task object (with empty messages initially)
                                                                     captured_pruned_summaries,
                                                                     all_previous_tasks_for_reprompt, // History before this reprompt
                                                                     initial_messages_for_reprompt, // Specific messages for this reprompt task
                                                                     ordered_files_for_test_async, // Current state of files
                                                                     current_path_for_test_async.clone(), // Pass current_path
                                                                     operation_signal_tx_for_autotest_async
                                                                         .clone(),
                                                                     app.task_retry_count, // is_retry = true
                                                                     None, // forced_research_content
                                                                 );
                                                            // Send signal to TUI to update its state for the reprompt
                                                            if operation_signal_tx_for_autotest_async.send(ProcessingSignal::AutoTestReprompting { new_task_for_reprompt: new_reprompt_task_object, abort_handle: reprompt_abort_handle }).await.is_err() { log::error!("Failed to send AutoTestReprompting signal"); }
                                                        } else {
                                                            let success_msg_for_log = format!("✅ Auto-Test PASSED: `{}`", app_config_for_test_async.auto_test_command);
                                                            log::info!("{}", success_msg_for_log);
                                                            execute_notification_command(&app_config_for_test_async.notification_command, &current_path_for_test_async, "✅ LLEdit Auto-test Passed", "Auto-test passed.");
                                                            if operation_signal_tx_for_autotest_async.send(ProcessingSignal::AutoTestPassed).await.is_err() { log::warn!("Failed to send AutoTestPassed signal"); }
                                                        }
                                                    }
                                                    Ok(Err(e)) => { log::error!("Failed to execute auto-test command `{}`: {}", app_config_for_test_async.auto_test_command, e); if operation_signal_tx_for_autotest_async.send(ProcessingSignal::AutoTestPassed).await.is_err() { log::warn!("Failed to send AutoTestPassed signal after command execution error."); } }
                                                    Err(e) => { log::error!("Failed to spawn auto-test command execution task for `{}`: {}", app_config_for_test_async.auto_test_command, e); if operation_signal_tx_for_autotest_async.send(ProcessingSignal::AutoTestPassed).await.is_err() { log::warn!("Failed to send AutoTestPassed signal after task spawn error."); } }
                                                }
                                            });
                                        } else { // No auto-test and no pending research (pending research is handled within cycle)
                                            app.is_processing = false;
                                            app.processing_progress = 1.0;
                                            app.show_success_state_until = Some(std::time::Instant::now() + std::time::Duration::from_secs(3));

                                            // Log metrics for this successful task.
                                            let edit_task_duration_str_for_notification =
                                                log_attempt_completion_metrics(&mut app, "Edit Task (No Auto-Test)");

                                            // Log final total duration for the entire sequence and update result_handler.
                                            if let Some(original_start_time) = app.original_task_start_time.take() {
                                                let total_duration = original_start_time.elapsed();
                                                result_handler.total_duration_ms_for_last_edit_task = Some(total_duration.as_millis());
                                            }
                                            result_handler.total_retries_for_last_edit_task = Some(app.task_retry_count);
                                            app.task_retry_count = 0;

                                            // Send notification
                                            let notification_title = "💾 LLEdit Edits Applied".to_string();
                                            let notification_content = if !edit_task_duration_str_for_notification.is_empty() {
                                                format!("Edit Round Took: {}.\n{}", edit_task_duration_str_for_notification, edit_summary_for_log)
                                            } else {
                                                edit_summary_for_log.clone()
                                            };
                                            log::debug!("{}", "-".repeat(70));
                                            execute_notification_command(
                                                &app.app_config.notification_command,
                                                &app.current_path,
                                                &notification_title,
                                                &notification_content,
                                            );

                                            // Refresh files from disk using OrderedFiles
                                            app.ordered_files.refresh_all_files_from_filesystem().await;

                                             // Add the successful task to result_handler and set as last_completed_task
                                             if let Some(task) = app.tasks.last().cloned() {
                                                 result_handler.add_task(&task);
                                                 app.last_completed_task = Some(task);
                                             }

                                            if app.app_config.exit_on_success {
                                                app.should_quit = true;
                                            }
                                        }
                                    }
                                    Err(err_msg) => {
                                        app.is_processing = false;
                                        log::error!("Processing failed: {}", err_msg);
                                        app.is_auto_researching = false;

                                        if let Some(task) = app.tasks.last() {
                                            result_handler.add_task(task);
                                        }
                                        if let Some(idx) = app.current_processing_target_index {
                                            if let Some(item) = app.editing_plan_items.get_mut(idx) {
                                                if item.status == EditPlanItemStatus::Processing {
                                                    item.status = EditPlanItemStatus::Failure;
                                                }
                                            }
                                        }
                                        // For other items, their status from TargetProcessed (Success/Failure) or Pending remains.
                                        // The plan will be cleared when show_success_state_until (which is None here) would expire,
                                        // or rather, when is_processing becomes false and there's no success timer.
                                        // Let's explicitly clear it here on error if not showing success.
                                        if app.show_success_state_until.is_none() { // Clear plan if not showing success
                                            app.clear_editing_plan();
                                        }

                                        if let Some(current_task) = app.tasks.last_mut() { // Should target the edit task
                                            current_task.add_message(crate::llm::ChatMessage {
                                                role: crate::llm::ChatRole::System,
                                                content: format!("Error during processing: {}", err_msg),
                                                message_type: crate::llm::MessageType::Text,
                                            });
                                        }
                                        app.processing_progress = 0.0; // Reset progress on error
                                        // app.show_success_state_until = None; // Already None or will be handled by Tick
                                    }
                                }
                            }
                            // Handle Research Signals
                            ProcessingSignal::ResearchTurnComplete { task_id, turn_number, messages_this_turn } => {
                                if Some(&task_id) == app.current_research_task_id.as_ref() {
                                    if let Some(task) = app.tasks.iter_mut().find(|t| t.id == task_id) {
                                        for msg in messages_this_turn {
                                            task.add_message(msg);
                                        }
                                    }
                                    app.research_progress = (turn_number as f32 / crate::research::research_cycle::MAX_RESEARCH_TURNS as f32).clamp(0.0, 0.9);
                                    log::trace!("Research task '{}', turn {} complete.", task_id, turn_number);
                                }
                            }
                            ProcessingSignal::ResearchComplete(result) => {
                                app.is_researching = false;
                                app.is_asking_question = false; // Ensure question state is also reset
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reset to default
                                app.current_research_abort_handle = None;
                                app.research_progress = 1.0; // Mark as complete

                                match result {
                                    Ok(research_success) => {
                                        if let Some(task) = app.tasks.iter_mut().find(|t| t.id == research_success.task_id) {
                                            for msg in research_success.final_messages {
                                                task.add_message(msg);
                                            }
                                            task.summary = Some(format!("Research complete, {} files found.", research_success.categorized_files.all_discovered.len()));
                                        }
                                        // Add discovered files to OrderedFiles
                                        app.ordered_files.add_categorized_files_from_research(&research_success.categorized_files).await;
                                        // Apply categorization might be redundant if add_categorized_files_from_research handles it,
                                        // but explicit call ensures categories are set if files were already known.
                                        app.ordered_files.apply_research_categorization(&research_success.categorized_files);

                                        // Add the completed research task to result_handler
                                        if let Some(task) = app.tasks.iter().find(|t| t.id == research_success.task_id) {
                                            result_handler.add_task(task);
                                        }
                                        // app.current_categorization is removed. Categorization is managed by OrderedFiles.
                                        result_handler.research_discovered_files = Some(research_success.categorized_files.all_discovered.clone());
                                        log::debug!("Total Bash Commands Issued for research task '{}': {}", research_success.task_id, app.research_bash_commands_issued);
                                        // app.research_bash_commands_issued = 0; // Reset for next research, if any (handled at start of research)
                                        if app.app_config.exit_on_success {
                                            app.should_quit = true;
                                        }
                                    }
                                    Err(err_msg) => {
                                        if let Some(task_id) = &app.current_research_task_id { // Log count even on failure
                                            log::debug!("Total Bash Commands Issued for (failed) research task '{}': {}", task_id, app.research_bash_commands_issued);
                                            // app.research_bash_commands_issued = 0; // Reset for next research (handled at start of research)
                                           if let Some(task) = app.tasks.iter_mut().find(|t| t.id == *task_id) {
                                                task.add_message(crate::llm::ChatMessage {
                                                    role: crate::llm::ChatRole::System,
                                                    content: format!("Research failed: {}", err_msg),
                                                    message_type: crate::llm::MessageType::Text,
                                                });
                                                task.summary = Some("Research failed.".to_string());
                                                // Add the failed research task to result_handler
                                                result_handler.add_task(task);
                                            }
                                        }
                                    }
                                }
                                app.current_research_task_id = None;
                            }
                            // AutomaticResearchComplete case removed
                            ProcessingSignal::ResearchBashCommandExecuted => {
                                if app.is_researching || app.is_auto_researching {
                                    app.research_bash_commands_issued += 1;
                                }
                            }
                            ProcessingSignal::ExpertModelStarted { model_alias } => {
                                app.current_operation_model_alias = Some(model_alias);
                            }
                            ProcessingSignal::ExpertModelEnded => {
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone());
                            }
                            ProcessingSignal::DelimiterCheckStarted { model_alias } => {
                                app.current_operation_model_alias = Some(model_alias);
                            }
                            ProcessingSignal::DelimiterCheckEnded => {
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone());
                            }
                            ProcessingSignal::SyntaxCheckStarted { model_alias } => {
                                app.current_operation_model_alias = Some(model_alias);
                            }
                            ProcessingSignal::SyntaxCheckEnded => {
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone());
                            }
                            ProcessingSignal::QuestionAnswerReceived(result) => {
                                app.is_asking_question = false; // Question finished
                                app.is_processing = false;      // Ensure edit processing state is off
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reset to default
                                app.current_task_abort_handle = None;
                                // app.processing_progress is not used for questions.
                                app.show_success_state_until = Some(std::time::Instant::now() + std::time::Duration::from_secs(1));

                                match result {
                                    Ok((question, answer)) => {
                                        // Use typewriter effect for LLM responses
                                        crate::tui_typewriter_info!(1, "{}", answer);
                                        let new_qna_pair = crate::interactive::app::QuestionAnswerPair { question: question.clone(), answer: answer.clone() };
                                        if app.question_answer_history.last() != Some(&new_qna_pair) {
                                            app.question_answer_history.push(new_qna_pair);
                                            // Direct update to result_handler.question_answer_history removed.
                                        } else {
                                            log::warn!("Attempted to push duplicate Q&A pair to app.question_answer_history. Skipping.");
                                        }

                                        let mut duration_str = "".to_string();
                                        if let Some(start_time) = app.current_task_start_time.take() {
                                            let elapsed = start_time.elapsed();
                                            let secs = elapsed.as_secs();
                                            let mins = secs / 60;
                                            let secs_remainder = secs % 60;
                                            duration_str = format!(" [{}m {}s]", mins, secs_remainder);
                                        }
                                        let notification_title = format!("💬 LLEdit Question Answered{}", duration_str);
                                        execute_notification_command(
                                            &app.app_config.notification_command,
                                            &app.current_path,
                                            &notification_title,
                                            &truncate_with_ellipsis(&answer, 100),
                                        );
                                        if app.app_config.exit_on_success {
                                            app.should_quit = true;
                                        }
                                    }
                                    Err(_err_msg) => {
                                        // Do not exit on error
                                    }
                                }
                            }
                            ProcessingSignal::QuestionAnswerAndAddToTaskInfoReceived(result) => {
                                app.is_asking_question = false; // Question finished
                                app.is_processing = false;      // Ensure edit processing state is off
                                app.current_operation_model_alias = Some(app.app_config.default_model.clone()); // Reset to default
                                app.current_task_abort_handle = None;
                                app.show_success_state_until = Some(std::time::Instant::now() + std::time::Duration::from_secs(1));

                                match result {
                                    Ok((question, answer)) => {
                                        // Use typewriter effect for LLM responses
                                        crate::tui_typewriter_info!(1, "{}", answer);
                                        let new_qna_pair = crate::interactive::app::QuestionAnswerPair { question: question.clone(), answer: answer.clone() };
                                        if app.question_answer_history.last() != Some(&new_qna_pair) {
                                            app.question_answer_history.push(new_qna_pair);
                                            // Direct update to result_handler.question_answer_history removed.
                                        } else {
                                            log::warn!("Attempted to push duplicate Q&A pair for task info to app.question_answer_history. Skipping.");
                                        }

                                        let task_info_update = format!("\n\n--- LLM Answer to Question ---\n{}\n--- End of LLM Answer ---", answer);
                                        match app.app_config.task_info.as_mut() {
                                            Some(existing_info) => {
                                                existing_info.push_str(&task_info_update);
                                            }
                                            None => {
                                                app.app_config.task_info = Some(task_info_update.trim_start().to_string());
                                            }
                                        }
                                        log::info!("Task info updated with LLM response.");
                                        let mut duration_str = "".to_string();
                                        if let Some(start_time) = app.current_task_start_time.take() {
                                            let elapsed = start_time.elapsed();
                                            let secs = elapsed.as_secs();
                                            let mins = secs / 60;
                                            let secs_remainder = secs % 60;
                                            duration_str = format!(" [{}m {}s]", mins, secs_remainder);
                                        }
                                        let notification_title = format!("💬 LLEdit Answer Added to Task Info{}", duration_str);
                                        execute_notification_command(
                                            &app.app_config.notification_command,
                                            &app.current_path,
                                            &notification_title,
                                            &truncate_with_ellipsis(&answer, 100),
                                        );
                                        if app.app_config.exit_on_success {
                                            app.should_quit = true;
                                        }
                                    }
                                    Err(err_msg) => {
                                        log::error!("Failed to get answer for task info: {}", err_msg);
                                        // Do not exit on error
                                    }
                                }
                            }
                        }
                    }
                    None => { // Operation signal channel closed
                        log::debug!("Operation signal channel closed. No more async task updates expected.");
                        if !app.is_processing && !app.is_researching && !app.is_asking_question {
                            // Normal if no operations are active.
                        }
                    }
                }
            }
        }

        if app.should_quit {
            break;
        }
    }

    // --- Restore Terminal ---
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    info!("Exiting interactive mode. Saving state...");

    // Save session if enabled
    if let Err(e) = crate::config::save_session(
        &app.app_config,
        &app.tasks,
        &app.ordered_files,
        &app.input_history,
    )
    .await
    {
        error!("Failed to save session state: {}", e);
    }

    // Save main message history (this is for results_input_file, not session state)
    if let Err(e) = save_message_history(&app.tasks, &app.app_config.results_input_file).await {
        error!("Failed to save results file: {}", e);
    }

    // Populate ResultHandler with all tasks before returning, if not already added by specific handlers.
    // This ensures all tasks are captured, especially if the app quits unexpectedly or via Ctrl+Q.
    // However, tasks are added to result_handler by specific signal handlers now.
    // For a clean exit (Ctrl+Q or normal termination), ensure all tasks are in app.tasks.
    // Then, iterate app.tasks and add to result_handler if not already present (by ID).
    let mut task_ids_in_result_handler = std::collections::HashSet::new();
    for task in &result_handler.tasks {
        task_ids_in_result_handler.insert(task.id.clone());
    }
    for app_task in &app.tasks {
        if !task_ids_in_result_handler.contains(&app_task.id) {
            result_handler.add_task(app_task);
        }
    }

    // Populate question_answer_history in result_handler
    // Ensure it's clean before populating from app's state.
    log::debug!(
        "Finalizing result_handler. App Q&A history length: {}. Last command type: {:?}",
        app.question_answer_history.len(),
        app.last_significant_command
    );
    if let Some(last_qna) = app.question_answer_history.last() {
        log::debug!(
            "Last Q&A in app.question_answer_history: Q: {:.50}..., A: {:.50}...",
            last_qna.question,
            last_qna.answer
        );
    }

    result_handler.question_answer_history.clear();
    for qna_pair_app_format in &app.question_answer_history {
        let serializable_qna_pair: crate::result::SerializableQuestionAnswerPair =
            qna_pair_app_format.into();
        if !result_handler
            .question_answer_history
            .contains(&serializable_qna_pair)
        {
            result_handler
                .question_answer_history
                .push(serializable_qna_pair);
        } else {
            log::warn!(
                "Skipping duplicate Q&A pair during final population of result_handler: Q: {:.50}...",
                serializable_qna_pair.question
            );
        }
    }
    log::debug!(
        "result_handler.question_answer_history populated. Length: {}",
        result_handler.question_answer_history.len()
    );

    // If no specific command was set, but there are tasks, assume it was an edit flow.
    let final_command_type = if app.last_significant_command.is_some() {
        app.last_significant_command
    } else if !result_handler.tasks.is_empty() {
        Some(LastSignificantCommandType::Edit)
    } else {
        None
    };

    Ok((result_handler, final_command_type)) // Return the populated AppResult and last command type
}

// Helper function to log current attempt duration and overall progress metrics.
// Takes current_task_start_time from app.
// Returns the duration string for the current attempt, for potential use in notifications.
fn log_attempt_completion_metrics(app: &mut InteractiveApp, attempt_description: &str) -> String {
    log::debug!("{}", "-".repeat(70)); // Log separator

    // Log duration of the current attempt and define attempt_duration_str
    let attempt_duration_str = if let Some(start_time) = app.current_task_start_time.take() {
        // This attempt is now considered complete
        let duration = start_time.elapsed();
        log::debug!(
            "{} Took: {}m {}s",
            attempt_description,
            duration.as_secs() / 60,
            duration.as_secs() % 60
        );
        format!("{}m {}s", duration.as_secs() / 60, duration.as_secs() % 60)
    } else {
        // This case should ideally not happen if current_task_start_time is managed correctly.
        log::warn!(
            "Could not determine duration for: {}. Current task start time was None.",
            attempt_description
        );
        "unknown".to_string() // Provide a default for notification string
    };

    // Log total running time "so far" (peeks original_task_start_time)
    if let Some(original_start_time_ref) = app.original_task_start_time.as_ref() {
        let total_duration_so_far = original_start_time_ref.elapsed();
        log::debug!(
            "Total Edit Task Duration: {}m {}s",
            total_duration_so_far.as_secs() / 60,
            total_duration_so_far.as_secs() % 60
        );
    } else {
        // This might happen if original_task_start_time was already taken or not set.
        log::trace!("Original task start time not available for 'Total Running Time' log.");
    }

    // Log total number of retries
    // app.task_retry_count reflects retries up to this point.
    // If this is a failed attempt, it includes this failure.
    // If this is a successful attempt, it's the count that led to success.
    if app.task_retry_count > 0 {
        log::debug!("Total Number Of Retries: {}", app.task_retry_count);
    }
    // If task_retry_count is 0, this log is skipped, which is fine.
    log::debug!("{}", "-".repeat(70)); // Level 3

    attempt_duration_str
}
